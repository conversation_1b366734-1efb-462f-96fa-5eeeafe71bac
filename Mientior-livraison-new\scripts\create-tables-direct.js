import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { resolve, dirname } from 'path';
import { fileURLToPath } from 'url';

// Charger les variables d'environnement
const envPath = resolve(dirname(fileURLToPath(import.meta.url)), '..', '.env');
dotenv.config({ path: envPath });

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Variables d\'environnement Supabase manquantes');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function createTablesDirectly() {
  console.log('🚀 Création directe des tables de monitoring...\n');
  
  try {
    // Pour contourner l'absence d'exec_sql, on va créer les tables via l'API REST
    // En utilisant une approche différente
    
    console.log('1. 📊 Tentative de création via requête SQL...');
    
    // Essayer d'utiliser une requête SQL simple
    const createAnalyticsSQL = `
      CREATE TABLE IF NOT EXISTS analytics_events (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        event_name VARCHAR(255) NOT NULL,
        user_id UUID,
        session_id VARCHAR(255) NOT NULL,
        properties JSONB DEFAULT '{}',
        timestamp TIMESTAMPTZ DEFAULT NOW(),
        platform VARCHAR(50) DEFAULT 'mobile',
        app_version VARCHAR(50) DEFAULT '1.0.0',
        user_role VARCHAR(50),
        created_at TIMESTAMPTZ DEFAULT NOW()
      );
    `;
    
    // Comme nous ne pouvons pas exécuter de DDL directement via l'API Supabase,
    // nous allons vérifier si les tables existent et donner des instructions
    
    console.log('2. 🔍 Vérification de l\'état actuel des tables...');
    
    const tables = [
      { name: 'analytics_events', description: 'Événements analytics' },
      { name: 'performance_reports', description: 'Rapports de performance' },
      { name: 'error_reports', description: 'Rapports d\'erreur' }
    ];
    
    const missingTables = [];
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase.from(table.name).select('count').limit(1);
        if (error) {
          console.log(`❌ Table ${table.name}: ${error.message}`);
          missingTables.push(table);
        } else {
          console.log(`✅ Table ${table.name}: existe déjà`);
        }
      } catch (err) {
        console.log(`❌ Table ${table.name}: erreur de vérification`);
        missingTables.push(table);
      }
    }
    
    if (missingTables.length > 0) {
      console.log('\n⚠️ Tables manquantes détectées!');
      console.log('\n🔧 SOLUTION: Exécuter le SQL suivant dans Supabase Dashboard:');
      console.log('   1. Aller sur https://supabase.com/dashboard');
      console.log('   2. Sélectionner votre projet');
      console.log('   3. Aller dans SQL Editor');
      console.log('   4. Exécuter le script suivant:\n');
      
      console.log('-- 📊 SCRIPT SQL POUR CRÉER LES TABLES DE MONITORING');
      console.log('-- Copier-coller ce script dans Supabase SQL Editor\n');
      
      console.log(`-- 1. Table analytics_events
CREATE TABLE IF NOT EXISTS analytics_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_name VARCHAR(255) NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  session_id VARCHAR(255) NOT NULL,
  properties JSONB DEFAULT '{}',
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  platform VARCHAR(50) DEFAULT 'mobile',
  app_version VARCHAR(50) DEFAULT '1.0.0',
  user_role VARCHAR(50),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_analytics_events_user_id ON analytics_events(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_event_name ON analytics_events(event_name);
CREATE INDEX IF NOT EXISTS idx_analytics_events_timestamp ON analytics_events(timestamp);

-- 2. Table performance_reports
CREATE TABLE IF NOT EXISTS performance_reports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  metric_type VARCHAR(100) NOT NULL CHECK (metric_type IN ('app_load', 'screen_load', 'api_response', 'payment_process', 'search_query')),
  metric_name VARCHAR(255) NOT NULL,
  value NUMERIC NOT NULL,
  unit VARCHAR(20) DEFAULT 'ms' CHECK (unit IN ('ms', 'seconds', 'bytes', 'count')),
  context VARCHAR(255),
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  session_id VARCHAR(255) NOT NULL,
  additional_data JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_performance_reports_metric_type ON performance_reports(metric_type);
CREATE INDEX IF NOT EXISTS idx_performance_reports_timestamp ON performance_reports(timestamp);

-- 3. Table error_reports
CREATE TABLE IF NOT EXISTS error_reports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  error_type VARCHAR(100) NOT NULL CHECK (error_type IN ('crash', 'api_error', 'network_error', 'validation_error', 'payment_error')),
  message TEXT NOT NULL,
  stack TEXT,
  context VARCHAR(255) NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  session_id VARCHAR(255) NOT NULL,
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  app_version VARCHAR(50) DEFAULT '1.0.0',
  device_info JSONB DEFAULT '{}',
  additional_data JSONB DEFAULT '{}',
  resolved BOOLEAN DEFAULT FALSE,
  resolved_at TIMESTAMPTZ,
  resolved_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_error_reports_error_type ON error_reports(error_type);
CREATE INDEX IF NOT EXISTS idx_error_reports_timestamp ON error_reports(timestamp);

-- 4. Activer RLS (Row Level Security)
ALTER TABLE analytics_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE performance_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE error_reports ENABLE ROW LEVEL SECURITY;

-- 5. Politiques RLS basiques (optionnel)
CREATE POLICY "Users can insert their own analytics" ON analytics_events
  FOR INSERT WITH CHECK (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can insert their own performance reports" ON performance_reports
  FOR INSERT WITH CHECK (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can insert their own error reports" ON error_reports
  FOR INSERT WITH CHECK (auth.uid() = user_id OR user_id IS NULL);`);
      
      console.log('\n📋 INSTRUCTIONS:');
      console.log('1. Copier le script SQL ci-dessus');
      console.log('2. Aller dans Supabase Dashboard > SQL Editor');
      console.log('3. Coller et exécuter le script');
      console.log('4. Redémarrer l\'application React Native');
      console.log('\n✅ Après cela, le monitoring sera pleinement fonctionnel!');
      
    } else {
      console.log('\n🎉 Toutes les tables de monitoring existent déjà!');
      console.log('✅ Le monitoring est prêt à être utilisé.');
    }
    
  } catch (error) {
    console.error('❌ Erreur lors de la vérification:', error.message);
  }
}

createTablesDirectly();
