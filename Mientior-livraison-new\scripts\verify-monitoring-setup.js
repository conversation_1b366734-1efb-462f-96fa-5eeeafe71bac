import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { resolve, dirname } from 'path';
import { fileURLToPath } from 'url';

// Charger les variables d'environnement
const envPath = resolve(dirname(fileURLToPath(import.meta.url)), '..', '.env');
dotenv.config({ path: envPath });

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Variables d\'environnement Supabase manquantes');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function verifyMonitoringSetup() {
  console.log('🔍 Vérification de la configuration du monitoring...\n');
  
  try {
    // 1. Vérifier la connexion Supabase
    console.log('1. 🔗 Test de connexion Supabase...');
    const { data: connectionTest, error: connectionError } = await supabase
      .from('users')
      .select('count')
      .limit(1);
    
    if (connectionError) {
      console.error('❌ Connexion Supabase échouée:', connectionError.message);
      return;
    }
    console.log('✅ Connexion Supabase OK');
    
    // 2. Vérifier les tables de monitoring
    console.log('\n2. 📊 Vérification des tables de monitoring...');
    
    const monitoringTables = [
      { name: 'analytics_events', description: 'Événements analytics' },
      { name: 'performance_reports', description: 'Rapports de performance' },
      { name: 'error_reports', description: 'Rapports d\'erreur' }
    ];
    
    const tableStatus = {};
    let allTablesExist = true;
    
    for (const table of monitoringTables) {
      try {
        const { data, error } = await supabase
          .from(table.name)
          .select('count')
          .limit(1);
        
        if (error) {
          console.log(`❌ Table ${table.name}: ${error.message}`);
          tableStatus[table.name] = false;
          allTablesExist = false;
        } else {
          console.log(`✅ Table ${table.name}: accessible`);
          tableStatus[table.name] = true;
        }
      } catch (err) {
        console.log(`❌ Table ${table.name}: erreur de vérification`);
        tableStatus[table.name] = false;
        allTablesExist = false;
      }
    }
    
    // 3. Test d'insertion si les tables existent
    if (allTablesExist) {
      console.log('\n3. 🧪 Test d\'insertion d\'événements...');
      
      try {
        // Test analytics_events
        const { error: analyticsError } = await supabase
          .from('analytics_events')
          .insert({
            event_name: 'test_verification',
            session_id: 'verification_session',
            properties: { test: true, timestamp: new Date().toISOString() },
            platform: 'mobile',
            app_version: '1.0.0'
          });
        
        if (analyticsError) {
          console.log('❌ Test analytics:', analyticsError.message);
        } else {
          console.log('✅ Test analytics: insertion réussie');
        }
        
        // Test performance_reports
        const { error: performanceError } = await supabase
          .from('performance_reports')
          .insert({
            metric_type: 'app_load',
            metric_name: 'test_verification',
            value: 123,
            unit: 'ms',
            context: 'verification_test',
            session_id: 'verification_session'
          });
        
        if (performanceError) {
          console.log('❌ Test performance:', performanceError.message);
        } else {
          console.log('✅ Test performance: insertion réussie');
        }
        
        // Test error_reports
        const { error: errorReportError } = await supabase
          .from('error_reports')
          .insert({
            error_type: 'validation_error',
            message: 'Test verification error',
            context: 'verification_test',
            session_id: 'verification_session',
            app_version: '1.0.0',
            device_info: { test: true },
            additional_data: { verification: true }
          });
        
        if (errorReportError) {
          console.log('❌ Test error reports:', errorReportError.message);
        } else {
          console.log('✅ Test error reports: insertion réussie');
        }
        
      } catch (testError) {
        console.error('❌ Erreur lors des tests:', testError.message);
      }
    }
    
    // 4. Résumé et recommandations
    console.log('\n📋 RÉSUMÉ DE LA VÉRIFICATION:');
    console.log('================================');
    
    if (allTablesExist) {
      console.log('🎉 EXCELLENT! Toutes les tables de monitoring sont configurées.');
      console.log('✅ Le monitoring est pleinement fonctionnel.');
      console.log('\n📊 Fonctionnalités disponibles:');
      console.log('  - Suivi des événements utilisateur');
      console.log('  - Monitoring des performances');
      console.log('  - Rapports d\'erreur automatiques');
      console.log('\n🚀 Redémarrez l\'application pour voir les améliorations!');
      
    } else {
      console.log('⚠️ CONFIGURATION INCOMPLÈTE - Tables manquantes détectées.');
      console.log('\n📋 Tables manquantes:');
      
      for (const table of monitoringTables) {
        if (!tableStatus[table.name]) {
          console.log(`  ❌ ${table.name} - ${table.description}`);
        }
      }
      
      console.log('\n🔧 SOLUTION:');
      console.log('1. Ouvrir Supabase Dashboard');
      console.log('2. Aller dans SQL Editor');
      console.log('3. Exécuter le script dans docs/SETUP_MONITORING_TABLES.md');
      console.log('4. Relancer cette vérification');
      
      console.log('\n📖 Guide détaillé: docs/SETUP_MONITORING_TABLES.md');
    }
    
    // 5. État actuel de l'application
    console.log('\n🔍 ÉTAT ACTUEL DE L\'APPLICATION:');
    console.log('================================');
    
    if (allTablesExist) {
      console.log('✅ Monitoring: Activé');
      console.log('✅ Analytics: Fonctionnels');
      console.log('✅ Performance: Suivi actif');
      console.log('✅ Erreurs: Rapports automatiques');
    } else {
      console.log('⚠️ Monitoring: Désactivé (tables manquantes)');
      console.log('⚠️ Analytics: Mode développement uniquement');
      console.log('⚠️ Performance: Logs console uniquement');
      console.log('⚠️ Erreurs: Pas de persistance');
    }
    
    console.log('\n💡 PROCHAINES ÉTAPES:');
    if (allTablesExist) {
      console.log('1. Redémarrer l\'application React Native');
      console.log('2. Vérifier la disparition des avertissements');
      console.log('3. Tester les fonctionnalités de l\'app');
      console.log('4. Consulter les données dans Supabase Dashboard');
    } else {
      console.log('1. Créer les tables manquantes (voir guide)');
      console.log('2. Relancer cette vérification');
      console.log('3. Redémarrer l\'application');
    }
    
  } catch (error) {
    console.error('❌ Erreur lors de la vérification:', error.message);
  }
}

verifyMonitoringSetup();
