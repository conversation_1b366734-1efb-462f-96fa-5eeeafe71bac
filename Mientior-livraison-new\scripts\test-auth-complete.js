import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { resolve, dirname } from 'path';
import { fileURLToPath } from 'url';

// Charger les variables d'environnement
const envPath = resolve(dirname(fileURLToPath(import.meta.url)), '..', '.env');
dotenv.config({ path: envPath });

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Variables d\'environnement Supabase manquantes');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testAuthentication() {
  console.log('🧪 Test complet d\'authentification...\n');
  
  const testEmail = `testauth${Date.now()}@gmail.com`;
  const testPassword = 'TestPassword123!';
  const testPhone = '+221771234567';
  
  try {
    // Test 1: Inscription avec email
    console.log('1. Test d\'inscription avec email...');
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          full_name: 'Test User Auth',
          phone: testPhone,
          role: 'client',
        }
      }
    });
    
    if (signUpError) {
      console.error('❌ Erreur inscription:', signUpError.message);
      return;
    }
    
    console.log('✅ Inscription réussie');
    console.log('User ID:', signUpData.user?.id);
    console.log('Email confirmé:', signUpData.user?.email_confirmed_at ? 'Oui' : 'Non');
    
    // Test 2: Vérifier la création du profil utilisateur
    console.log('\n2. Test de création du profil utilisateur...');
    if (signUpData.user) {
      const { data: profile, error: profileError } = await supabase
        .from('users')
        .select('*')
        .eq('id', signUpData.user.id)
        .single();
      
      if (profileError) {
        console.error('❌ Erreur récupération profil:', profileError.message);
      } else if (profile) {
        console.log('✅ Profil utilisateur créé');
        console.log('Nom:', profile.full_name);
        console.log('Rôle:', profile.role);
        console.log('Statut:', profile.status);
      } else {
        console.log('⚠️ Profil utilisateur non trouvé');
      }
    }
    
    // Test 3: Connexion avec email/password
    console.log('\n3. Test de connexion avec email/password...');
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: testEmail,
      password: testPassword,
    });
    
    if (signInError) {
      console.error('❌ Erreur connexion:', signInError.message);
    } else {
      console.log('✅ Connexion réussie');
      console.log('Session active:', !!signInData.session);
      console.log('Access token présent:', !!signInData.session?.access_token);
    }
    
    // Test 4: Récupérer l'utilisateur actuel
    console.log('\n4. Test de récupération utilisateur actuel...');
    const { data: { user }, error: getUserError } = await supabase.auth.getUser();
    
    if (getUserError) {
      console.error('❌ Erreur getUser:', getUserError.message);
    } else if (user) {
      console.log('✅ Utilisateur récupéré');
      console.log('Email:', user.email);
      console.log('ID:', user.id);
      console.log('Métadonnées:', user.user_metadata);
    } else {
      console.log('⚠️ Aucun utilisateur connecté');
    }
    
    // Test 5: Déconnexion
    console.log('\n5. Test de déconnexion...');
    const { error: signOutError } = await supabase.auth.signOut();
    
    if (signOutError) {
      console.error('❌ Erreur déconnexion:', signOutError.message);
    } else {
      console.log('✅ Déconnexion réussie');
    }
    
    // Test 6: Vérifier que l'utilisateur est déconnecté
    console.log('\n6. Vérification de la déconnexion...');
    const { data: { user: userAfterSignOut } } = await supabase.auth.getUser();
    
    if (userAfterSignOut) {
      console.log('⚠️ Utilisateur encore connecté après déconnexion');
    } else {
      console.log('✅ Utilisateur correctement déconnecté');
    }
    
    console.log('\n🎉 Tests d\'authentification terminés avec succès!');
    
  } catch (error) {
    console.error('❌ Erreur générale:', error.message);
  }
}

// Test des fonctions d'authentification du service
async function testAuthService() {
  console.log('\n🔧 Test du service d\'authentification...\n');
  
  try {
    // Simuler l'import du service d'authentification
    const authService = {
      async signUp(email, password, userData) {
        const { data, error } = await supabase.auth.signUp({
          email,
          password,
          options: {
            data: {
              full_name: userData.full_name,
              phone: userData.phone,
              role: userData.role || 'client',
            }
          }
        });
        
        if (error) throw error;
        
        // Créer le profil utilisateur dans la table users
        if (data.user) {
          const { error: profileError } = await supabase
            .from('users')
            .insert({
              id: data.user.id,
              email: data.user.email,
              phone: userData.phone,
              role: userData.role || 'client',
              full_name: userData.full_name,
              is_active: true,
              status: 'en_verification',
              preferred_language: 'fr',
            });
          
          if (profileError) {
            console.error('Erreur création profil:', profileError);
            throw profileError;
          }
        }
        
        return data;
      },
      
      async signIn(email, password) {
        const { data, error } = await supabase.auth.signInWithPassword({
          email,
          password,
        });
        
        if (error) throw error;
        return data;
      },
      
      async getCurrentUser() {
        const { data: { user }, error: authError } = await supabase.auth.getUser();
        
        if (authError || !user) return null;
        
        const { data: profile, error } = await supabase
          .from('users')
          .select('*')
          .eq('id', user.id)
          .maybeSingle();
        
        if (error || !profile) {
          // Créer un profil temporaire
          return {
            id: user.id,
            email: user.email || '',
            phone: user.phone || '',
            full_name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'Utilisateur',
            role: 'client',
            avatar_url: user.user_metadata?.avatar_url,
            is_active: true,
            created_at: user.created_at,
            updated_at: new Date().toISOString(),
          };
        }
        
        return profile;
      }
    };
    
    const testEmail = `servicetest${Date.now()}@gmail.com`;
    const testPassword = 'ServiceTest123!';
    
    console.log('1. Test inscription via service...');
    await authService.signUp(testEmail, testPassword, {
      full_name: 'Service Test User',
      phone: '+221771234567',
      role: 'client'
    });
    console.log('✅ Inscription via service réussie');
    
    console.log('\n2. Test connexion via service...');
    await authService.signIn(testEmail, testPassword);
    console.log('✅ Connexion via service réussie');
    
    console.log('\n3. Test récupération utilisateur via service...');
    const currentUser = await authService.getCurrentUser();
    if (currentUser) {
      console.log('✅ Utilisateur récupéré via service');
      console.log('Nom:', currentUser.full_name);
      console.log('Rôle:', currentUser.role);
    } else {
      console.log('❌ Impossible de récupérer l\'utilisateur');
    }
    
    console.log('\n🎉 Tests du service d\'authentification terminés!');
    
  } catch (error) {
    console.error('❌ Erreur test service:', error.message);
  }
}

// Exécuter tous les tests
async function runAllTests() {
  await testAuthentication();
  await testAuthService();
}

runAllTests();
