import { createClient } from '@supabase/supabase-js';
import { promises as fs } from 'fs';
import { join, dirname, resolve } from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Charger les variables d'environnement
const envPath = resolve(dirname(fileURLToPath(import.meta.url)), '..', '.env');
dotenv.config({ path: envPath });

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Configuration Supabase
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Variables d\'environnement Supabase manquantes');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function setupMonitoringTables() {
  console.log('🚀 Configuration des tables de monitoring...\n');
  
  try {
    // 1. Créer la table analytics_events
    console.log('1. 📊 Création de la table analytics_events...');
    const { error: analyticsError } = await supabase.rpc('exec_sql', {
      sql_query: `
        CREATE TABLE IF NOT EXISTS analytics_events (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          event_name VARCHAR(255) NOT NULL,
          user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
          session_id VARCHAR(255) NOT NULL,
          properties JSONB DEFAULT '{}',
          timestamp TIMESTAMPTZ DEFAULT NOW(),
          platform VARCHAR(50) DEFAULT 'mobile',
          app_version VARCHAR(50) DEFAULT '1.0.0',
          user_role VARCHAR(50),
          created_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        CREATE INDEX IF NOT EXISTS idx_analytics_events_user_id ON analytics_events(user_id);
        CREATE INDEX IF NOT EXISTS idx_analytics_events_event_name ON analytics_events(event_name);
        CREATE INDEX IF NOT EXISTS idx_analytics_events_timestamp ON analytics_events(timestamp);
        CREATE INDEX IF NOT EXISTS idx_analytics_events_session_id ON analytics_events(session_id);
      `
    });
    
    if (analyticsError) {
      console.warn('⚠️ Analytics table:', analyticsError.message);
    } else {
      console.log('✅ Table analytics_events créée');
    }
    
    // 2. Créer la table performance_reports
    console.log('\n2. 📈 Création de la table performance_reports...');
    const { error: performanceError } = await supabase.rpc('exec_sql', {
      sql_query: `
        CREATE TABLE IF NOT EXISTS performance_reports (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          metric_type VARCHAR(100) NOT NULL CHECK (metric_type IN ('app_load', 'screen_load', 'api_response', 'payment_process', 'search_query')),
          metric_name VARCHAR(255) NOT NULL,
          value NUMERIC NOT NULL,
          unit VARCHAR(20) DEFAULT 'ms' CHECK (unit IN ('ms', 'seconds', 'bytes', 'count')),
          context VARCHAR(255),
          timestamp TIMESTAMPTZ DEFAULT NOW(),
          user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
          session_id VARCHAR(255) NOT NULL,
          additional_data JSONB DEFAULT '{}',
          created_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        CREATE INDEX IF NOT EXISTS idx_performance_reports_metric_type ON performance_reports(metric_type);
        CREATE INDEX IF NOT EXISTS idx_performance_reports_metric_name ON performance_reports(metric_name);
        CREATE INDEX IF NOT EXISTS idx_performance_reports_timestamp ON performance_reports(timestamp);
        CREATE INDEX IF NOT EXISTS idx_performance_reports_user_id ON performance_reports(user_id);
      `
    });
    
    if (performanceError) {
      console.warn('⚠️ Performance table:', performanceError.message);
    } else {
      console.log('✅ Table performance_reports créée');
    }
    
    // 3. Créer la table error_reports
    console.log('\n3. 🚨 Création de la table error_reports...');
    const { error: errorReportsError } = await supabase.rpc('exec_sql', {
      sql_query: `
        CREATE TABLE IF NOT EXISTS error_reports (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          error_type VARCHAR(100) NOT NULL CHECK (error_type IN ('crash', 'api_error', 'network_error', 'validation_error', 'payment_error')),
          message TEXT NOT NULL,
          stack TEXT,
          context VARCHAR(255) NOT NULL,
          user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
          session_id VARCHAR(255) NOT NULL,
          timestamp TIMESTAMPTZ DEFAULT NOW(),
          app_version VARCHAR(50) DEFAULT '1.0.0',
          device_info JSONB DEFAULT '{}',
          additional_data JSONB DEFAULT '{}',
          resolved BOOLEAN DEFAULT FALSE,
          resolved_at TIMESTAMPTZ,
          resolved_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
          created_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        CREATE INDEX IF NOT EXISTS idx_error_reports_error_type ON error_reports(error_type);
        CREATE INDEX IF NOT EXISTS idx_error_reports_timestamp ON error_reports(timestamp);
        CREATE INDEX IF NOT EXISTS idx_error_reports_user_id ON error_reports(user_id);
        CREATE INDEX IF NOT EXISTS idx_error_reports_resolved ON error_reports(resolved);
      `
    });
    
    if (errorReportsError) {
      console.warn('⚠️ Error reports table:', errorReportsError.message);
    } else {
      console.log('✅ Table error_reports créée');
    }
    
    // 4. Vérifier que les tables existent
    console.log('\n4. ✅ Vérification des tables créées...');
    
    const tables = ['analytics_events', 'performance_reports', 'error_reports'];
    for (const tableName of tables) {
      const { data, error } = await supabase.from(tableName).select('count').limit(1);
      if (error) {
        console.log(`❌ Table ${tableName}: ${error.message}`);
      } else {
        console.log(`✅ Table ${tableName}: accessible`);
      }
    }
    
    // 5. Tester l'insertion d'un événement de test
    console.log('\n5. 🧪 Test d\'insertion d\'événement...');
    const { error: insertError } = await supabase
      .from('analytics_events')
      .insert({
        event_name: 'test_setup',
        session_id: 'setup_session',
        properties: { setup: true },
        platform: 'mobile',
        app_version: '1.0.0'
      });
    
    if (insertError) {
      console.warn('⚠️ Test insertion:', insertError.message);
    } else {
      console.log('✅ Test d\'insertion réussi');
    }
    
    console.log('\n🎉 Configuration des tables de monitoring terminée!');
    console.log('\n📊 Tables disponibles:');
    console.log('  - analytics_events: Événements utilisateur');
    console.log('  - performance_reports: Métriques de performance');
    console.log('  - error_reports: Rapports d\'erreur');
    
    console.log('\n🔧 Le monitoring est maintenant activé!');
    
  } catch (error) {
    console.error('❌ Erreur lors de la configuration:', error.message);
    process.exit(1);
  }
}

setupMonitoringTables();
