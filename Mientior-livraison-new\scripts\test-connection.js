import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { resolve, dirname } from 'path';
import { fileURLToPath } from 'url';

// Charger les variables d'environnement
const envPath = resolve(dirname(fileURLToPath(import.meta.url)), '..', '.env');
dotenv.config({ path: envPath });

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

console.log('🔧 Configuration Supabase:');
console.log('URL:', supabaseUrl ? '✅ Configuré' : '❌ Manquant');
console.log('Key:', supabaseKey ? '✅ Configuré' : '❌ Manquant');

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Variables d\'environnement Supabase manquantes');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testConnection() {
  try {
    console.log('\n🧪 Test de connexion Supabase...');
    
    // Test 1: Vérifier la connexion de base
    console.log('1. Test de connexion de base...');
    const { data, error } = await supabase.from('users').select('count').limit(1);
    
    if (error) {
      console.error('❌ Erreur de connexion:', error.message);
      
      // Test si c'est un problème de table
      if (error.message.includes('relation "users" does not exist')) {
        console.log('⚠️ La table "users" n\'existe pas - problème de migration');
      }
    } else {
      console.log('✅ Connexion Supabase réussie');
    }
    
    // Test 2: Vérifier l'authentification
    console.log('\n2. Test d\'authentification...');
    const { data: authData, error: authError } = await supabase.auth.getSession();
    
    if (authError) {
      console.error('❌ Erreur auth:', authError.message);
    } else {
      console.log('✅ Service d\'authentification accessible');
      console.log('Session actuelle:', authData.session ? 'Connecté' : 'Non connecté');
    }
    
    // Test 3: Vérifier les tables essentielles
    console.log('\n3. Test des tables essentielles...');

    const essentialTables = ['users', 'client_profiles', 'merchant_profiles', 'delivery_profiles'];

    for (const tableName of essentialTables) {
      try {
        const { data, error } = await supabase.from(tableName).select('*').limit(1);
        if (error) {
          console.log(`❌ Table "${tableName}": ${error.message}`);
        } else {
          console.log(`✅ Table "${tableName}": accessible`);
        }
      } catch (err) {
        console.log(`❌ Table "${tableName}": erreur - ${err.message}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Erreur générale:', error.message);
  }
}

testConnection();
