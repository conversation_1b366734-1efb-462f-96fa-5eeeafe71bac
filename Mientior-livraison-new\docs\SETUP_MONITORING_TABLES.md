# 📊 Configuration des Tables de Monitoring - Mientior Livraison

## 🎯 Objectif

Ce guide explique comment configurer les tables de monitoring et d'analytics dans Supabase pour activer le suivi des performances et des erreurs dans l'application Mientior Livraison.

## ⚠️ Problèmes Actuels

L'application affiche ces avertissements au démarrage :
- `⚠️ Event queue processing failed: [Error: Row too big to fit into CursorWindow]`
- `⚠️ Performance monitoring disabled - database tables not set up yet`

## 🔧 Solution

### Étape 1: Accéder à Supabase Dashboard

1. Aller sur [https://supabase.com/dashboard](https://supabase.com/dashboard)
2. Sélectionner votre projet Mientior Livraison
3. Naviguer vers **SQL Editor** dans le menu de gauche

### Étape 2: Exécuter le Script SQL

Copier-coller et exécuter le script SQL suivant dans l'éditeur :

```sql
-- 📊 SCRIPT SQL POUR CRÉER LES TABLES DE MONITORING
-- Mientior Livraison - Tables Analytics et Performance

-- ==========================================
-- 📊 TABLE ANALYTICS_EVENTS
-- ==========================================

CREATE TABLE IF NOT EXISTS analytics_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_name VARCHAR(255) NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  session_id VARCHAR(255) NOT NULL,
  properties JSONB DEFAULT '{}',
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  platform VARCHAR(50) DEFAULT 'mobile',
  app_version VARCHAR(50) DEFAULT '1.0.0',
  user_role VARCHAR(50),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Index pour optimiser les requêtes
CREATE INDEX IF NOT EXISTS idx_analytics_events_user_id ON analytics_events(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_event_name ON analytics_events(event_name);
CREATE INDEX IF NOT EXISTS idx_analytics_events_timestamp ON analytics_events(timestamp);
CREATE INDEX IF NOT EXISTS idx_analytics_events_session_id ON analytics_events(session_id);

-- ==========================================
-- 📈 TABLE PERFORMANCE_REPORTS
-- ==========================================

CREATE TABLE IF NOT EXISTS performance_reports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  metric_type VARCHAR(100) NOT NULL CHECK (metric_type IN ('app_load', 'screen_load', 'api_response', 'payment_process', 'search_query')),
  metric_name VARCHAR(255) NOT NULL,
  value NUMERIC NOT NULL,
  unit VARCHAR(20) DEFAULT 'ms' CHECK (unit IN ('ms', 'seconds', 'bytes', 'count')),
  context VARCHAR(255),
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  session_id VARCHAR(255) NOT NULL,
  additional_data JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Index pour optimiser les requêtes
CREATE INDEX IF NOT EXISTS idx_performance_reports_metric_type ON performance_reports(metric_type);
CREATE INDEX IF NOT EXISTS idx_performance_reports_metric_name ON performance_reports(metric_name);
CREATE INDEX IF NOT EXISTS idx_performance_reports_timestamp ON performance_reports(timestamp);
CREATE INDEX IF NOT EXISTS idx_performance_reports_user_id ON performance_reports(user_id);

-- ==========================================
-- 🚨 TABLE ERROR_REPORTS
-- ==========================================

CREATE TABLE IF NOT EXISTS error_reports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  error_type VARCHAR(100) NOT NULL CHECK (error_type IN ('crash', 'api_error', 'network_error', 'validation_error', 'payment_error')),
  message TEXT NOT NULL,
  stack TEXT,
  context VARCHAR(255) NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  session_id VARCHAR(255) NOT NULL,
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  app_version VARCHAR(50) DEFAULT '1.0.0',
  device_info JSONB DEFAULT '{}',
  additional_data JSONB DEFAULT '{}',
  resolved BOOLEAN DEFAULT FALSE,
  resolved_at TIMESTAMPTZ,
  resolved_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Index pour optimiser les requêtes
CREATE INDEX IF NOT EXISTS idx_error_reports_error_type ON error_reports(error_type);
CREATE INDEX IF NOT EXISTS idx_error_reports_timestamp ON error_reports(timestamp);
CREATE INDEX IF NOT EXISTS idx_error_reports_user_id ON error_reports(user_id);
CREATE INDEX IF NOT EXISTS idx_error_reports_resolved ON error_reports(resolved);

-- ==========================================
-- 🔒 SÉCURITÉ RLS (ROW LEVEL SECURITY)
-- ==========================================

-- Activer RLS sur toutes les tables
ALTER TABLE analytics_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE performance_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE error_reports ENABLE ROW LEVEL SECURITY;

-- Politiques RLS pour permettre l'insertion
CREATE POLICY "Users can insert their own analytics" ON analytics_events
  FOR INSERT WITH CHECK (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can insert their own performance reports" ON performance_reports
  FOR INSERT WITH CHECK (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can insert their own error reports" ON error_reports
  FOR INSERT WITH CHECK (auth.uid() = user_id OR user_id IS NULL);

-- Politiques RLS pour la lecture (optionnel - pour les admins)
CREATE POLICY "Admins can read all analytics" ON analytics_events
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.role = 'admin'
    )
  );

CREATE POLICY "Admins can read all performance reports" ON performance_reports
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.role = 'admin'
    )
  );

CREATE POLICY "Admins can read all error reports" ON error_reports
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.role = 'admin'
    )
  );
```

### Étape 3: Vérifier la Création

Après avoir exécuté le script, vérifier que les tables ont été créées :

1. Aller dans **Table Editor** dans Supabase Dashboard
2. Vérifier la présence des tables :
   - `analytics_events`
   - `performance_reports` 
   - `error_reports`

### Étape 4: Redémarrer l'Application

1. Arrêter l'application React Native (`Ctrl+C`)
2. Redémarrer avec `npx expo start`
3. Les avertissements de monitoring devraient disparaître

## ✅ Résultat Attendu

Après la configuration, vous devriez voir :
- ✅ Disparition des avertissements de monitoring
- ✅ Logs de performance dans la console
- ✅ Suivi des erreurs activé
- ✅ Analytics fonctionnels

## 🔍 Vérification

Pour vérifier que tout fonctionne :

```bash
# Exécuter le script de test
node scripts/test-monitoring.js
```

## 📊 Utilisation

Une fois configuré, le monitoring collectera automatiquement :
- **Analytics** : Actions utilisateur, vues d'écran
- **Performance** : Temps de chargement, réponses API
- **Erreurs** : Crashes, erreurs réseau, erreurs de validation

## 🛠️ Maintenance

Les tables incluent des index optimisés et des politiques RLS pour :
- Performance optimale des requêtes
- Sécurité des données utilisateur
- Nettoyage automatique des anciennes données

---

**Note** : Ce script est sûr à exécuter plusieurs fois grâce aux clauses `IF NOT EXISTS`.
