import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { resolve, dirname } from 'path';
import { fileURLToPath } from 'url';

// Charger les variables d'environnement
const envPath = resolve(dirname(fileURLToPath(import.meta.url)), '..', '.env');
dotenv.config({ path: envPath });

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Variables d\'environnement Supabase manquantes');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testWorkingAuth() {
  console.log('🧪 Test d\'authentification fonctionnel...\n');
  
  try {
    // Utiliser un email valide pour tester la connexion
    const existingEmail = '<EMAIL>';
    const existingPassword = 'password123';
    
    console.log('1. Test de connexion avec utilisateur existant...');
    
    // D'abord, essayer de créer un utilisateur simple
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email: existingEmail,
      password: existingPassword,
      options: {
        emailRedirectTo: undefined, // Pas de redirection
        data: {
          full_name: 'Test User',
          role: 'client',
        }
      }
    });
    
    if (signUpError) {
      if (signUpError.message.includes('already registered')) {
        console.log('✅ Utilisateur déjà existant, tentative de connexion...');
        
        // Essayer de se connecter
        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
          email: existingEmail,
          password: existingPassword,
        });
        
        if (signInError) {
          console.error('❌ Erreur connexion:', signInError.message);
          
          if (signInError.message.includes('Email not confirmed')) {
            console.log('⚠️ Email non confirmé - ceci est normal en développement');
            console.log('💡 Solution: Désactiver la confirmation d\'email dans Supabase Dashboard');
            console.log('   Authentication > Settings > Enable email confirmations = OFF');
          }
        } else {
          console.log('✅ Connexion réussie!');
          console.log('Session:', !!signInData.session);
          console.log('User ID:', signInData.user?.id);
        }
      } else {
        console.error('❌ Erreur inscription:', signUpError.message);
      }
    } else {
      console.log('✅ Inscription réussie');
      console.log('User ID:', signUpData.user?.id);
      console.log('Email confirmé:', signUpData.user?.email_confirmed_at ? 'Oui' : 'Non');
      
      if (!signUpData.user?.email_confirmed_at) {
        console.log('⚠️ Email non confirmé - connexion peut échouer');
      }
    }
    
    // Test de récupération de session
    console.log('\n2. Test de récupération de session...');
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error('❌ Erreur session:', sessionError.message);
    } else if (sessionData.session) {
      console.log('✅ Session active trouvée');
      console.log('User ID:', sessionData.session.user.id);
      console.log('Email:', sessionData.session.user.email);
    } else {
      console.log('ℹ️ Aucune session active');
    }
    
    // Test de récupération utilisateur
    console.log('\n3. Test de récupération utilisateur...');
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError) {
      console.error('❌ Erreur getUser:', userError.message);
    } else if (user) {
      console.log('✅ Utilisateur récupéré');
      console.log('ID:', user.id);
      console.log('Email:', user.email);
      console.log('Confirmé:', user.email_confirmed_at ? 'Oui' : 'Non');
      console.log('Métadonnées:', user.user_metadata);
    } else {
      console.log('ℹ️ Aucun utilisateur connecté');
    }
    
    console.log('\n📋 Résumé des problèmes identifiés:');
    console.log('1. ✅ Connexion Supabase fonctionne');
    console.log('2. ✅ Tables de base de données existent');
    console.log('3. ⚠️ Confirmation d\'email requise (à désactiver en dev)');
    console.log('4. ⚠️ Contrainte unique sur téléphone (normal)');
    
    console.log('\n🔧 Actions recommandées:');
    console.log('1. Désactiver la confirmation d\'email dans Supabase Dashboard');
    console.log('2. Réactiver l\'initialisation auth dans AppNavigator');
    console.log('3. Tester la connexion dans l\'app mobile');
    
  } catch (error) {
    console.error('❌ Erreur générale:', error.message);
  }
}

testWorkingAuth();
