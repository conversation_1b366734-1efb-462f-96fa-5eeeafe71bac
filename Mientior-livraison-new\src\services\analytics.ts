// 📊 Analytics Service - Mientior Livraison
// Comprehensive analytics and monitoring for African delivery app

import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from './supabase';
// Firebase Analytics (when available)
// import analytics from '@react-native-firebase/analytics';
// Sentry for error tracking (when available)
// import * as Sentry from '@sentry/react-native';

// Types for analytics events
export interface AnalyticsEvent {
  event_name: string;
  user_id?: string;
  session_id: string;
  properties: Record<string, any>;
  timestamp: string;
  platform: 'mobile' | 'web';
  app_version: string;
  user_role?: 'client' | 'livreur' | 'commercant' | 'admin';
}

export interface BusinessMetrics {
  orders_placed: number;
  orders_completed: number;
  orders_cancelled: number;
  total_revenue: number;
  average_order_value: number;
  delivery_success_rate: number;
  payment_success_rate: number;
  user_retention_rate: number;
}

export interface PerformanceMetrics {
  app_load_time: number;
  screen_load_time: number;
  api_response_time: number;
  crash_rate: number;
  error_rate: number;
  network_error_rate: number;
}

// Analytics Service Class
class AnalyticsService {
  private sessionId: string = '';
  private userId: string | null = null;
  private userRole: string | null = null;
  private isInitialized: boolean = false;
  private eventQueue: AnalyticsEvent[] = [];
  private isOnline: boolean = true;

  // Initialize analytics service
  async initialize(userId?: string, userRole?: string): Promise<void> {
    try {
      this.sessionId = await this.generateSessionId();
      this.userId = userId || null;
      this.userRole = userRole || null;
      this.isInitialized = true;

      // Process any queued events
      await this.processEventQueue();
      
      console.log('📊 Analytics service initialized');
    } catch (error) {
      console.error('❌ Failed to initialize analytics:', error);
    }
  }

  // Generate unique session ID
  private async generateSessionId(): Promise<string> {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2);
    const sessionId = `session_${timestamp}_${random}`;
    
    await AsyncStorage.setItem('analytics_session_id', sessionId);
    return sessionId;
  }

  // Track custom events with size validation
  async trackEvent(
    eventName: string,
    properties: Record<string, any> = {},
    immediate: boolean = false
  ): Promise<void> {
    // Completely disable analytics in development mode to reduce noise
    if (__DEV__) {
      return;
    }

    try {
      // Sanitize properties to prevent oversized data
      const sanitizedProperties = this.sanitizeProperties(properties);

      const event: AnalyticsEvent = {
        event_name: eventName,
        user_id: this.userId,
        session_id: this.sessionId,
        properties: {
          ...sanitizedProperties,
          timestamp_local: new Date().toISOString(),
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        },
        timestamp: new Date().toISOString(),
        platform: 'mobile',
        app_version: '1.0.0', // TODO: Get from app.json
        user_role: this.userRole,
      };

      // Check event size before processing
      const eventSize = JSON.stringify(event).length;
      if (eventSize > 10000) { // 10KB limit
        console.warn('⚠️ Event too large, truncating:', eventName, `${eventSize} bytes`);
        event.properties = { event_truncated: true, original_size: eventSize };
      }

      if (immediate || this.isOnline) {
        await this.sendEvent(event);
      } else {
        // Queue event for later if offline, but limit queue size
        this.eventQueue.push(event);

        // Prevent queue from growing too large
        if (this.eventQueue.length > 100) {
          this.eventQueue = this.eventQueue.slice(-50); // Keep only last 50 events
        }

        await this.saveEventQueue();
      }
    } catch (error) {
      // Silently fail for now - analytics may not be fully set up
      if (__DEV__) {
        console.warn('⚠️ Event tracking failed:', eventName, error.message);
      }
    }
  }

  // Sanitize properties to prevent oversized data
  private sanitizeProperties(properties: Record<string, any>): Record<string, any> {
    const sanitized: Record<string, any> = {};

    for (const [key, value] of Object.entries(properties)) {
      try {
        const valueStr = JSON.stringify(value);
        if (valueStr.length > 1000) { // 1KB limit per property
          sanitized[key] = `[TRUNCATED: ${valueStr.length} bytes]`;
        } else {
          sanitized[key] = value;
        }
      } catch (error) {
        sanitized[key] = '[INVALID_DATA]';
      }
    }

    return sanitized;
  }

  // Send event to Supabase
  private async sendEvent(event: AnalyticsEvent): Promise<void> {
    try {
      const { error } = await supabase
        .from('analytics_events')
        .insert(event);

      if (error) throw error;
    } catch (error) {
      // Silently fail for now - analytics tables may not exist yet
      // Only log in development mode
      if (__DEV__) {
        console.warn('⚠️ Analytics event failed (table may not exist):', event.event_name);
      }
      // Don't re-queue to avoid infinite loops
    }
  }

  // Process queued events with better error handling
  private async processEventQueue(): Promise<void> {
    try {
      const savedQueue = await AsyncStorage.getItem('analytics_event_queue');
      if (savedQueue) {
        try {
          const events: AnalyticsEvent[] = JSON.parse(savedQueue);
          // Validate events before adding to queue
          const validEvents = events.filter(event => {
            return event && event.event_name && event.timestamp;
          });
          this.eventQueue = [...this.eventQueue, ...validEvents];
        } catch (parseError) {
          console.warn('⚠️ Invalid event queue data, clearing:', parseError.message);
          await AsyncStorage.removeItem('analytics_event_queue');
          return;
        }
      }

      // Send all queued events with individual error handling
      const successfulEvents: string[] = [];
      for (const event of this.eventQueue) {
        try {
          await this.sendEvent(event);
          successfulEvents.push(event.event_name);
        } catch (eventError) {
          // Log individual event failures but continue processing
          if (__DEV__) {
            console.warn('⚠️ Failed to send event:', event.event_name, eventError.message);
          }
        }
      }

      // Clear queue after processing (regardless of individual success/failure)
      this.eventQueue = [];
      await AsyncStorage.removeItem('analytics_event_queue');

      if (__DEV__ && successfulEvents.length > 0) {
        console.log('📊 Processed analytics events:', successfulEvents.length);
      }

    } catch (error) {
      // Handle critical errors by clearing the queue
      if (__DEV__) {
        console.warn('⚠️ Event queue processing failed, clearing queue:', error.message);
      }

      this.eventQueue = [];
      try {
        await AsyncStorage.removeItem('analytics_event_queue');
      } catch (clearError) {
        // Ignore clear errors
      }
    }
  }

  // Save event queue to storage with size limits
  private async saveEventQueue(): Promise<void> {
    try {
      // Limit queue size to prevent storage issues
      const maxQueueSize = 50; // Maximum 50 events in queue
      const maxEventSize = 10000; // Maximum 10KB per event

      // Filter out oversized events and limit queue size
      const filteredQueue = this.eventQueue
        .filter(event => {
          const eventSize = JSON.stringify(event).length;
          if (eventSize > maxEventSize) {
            console.warn('⚠️ Event too large, skipping:', event.event_name, `${eventSize} bytes`);
            return false;
          }
          return true;
        })
        .slice(-maxQueueSize); // Keep only the last N events

      // Update the queue with filtered events
      this.eventQueue = filteredQueue;

      const queueData = JSON.stringify(filteredQueue);
      const queueSize = queueData.length;

      // Check total size before saving (SQLite limit is ~2MB)
      if (queueSize > 1000000) { // 1MB limit for safety
        console.warn('⚠️ Event queue too large, clearing oldest events');
        this.eventQueue = filteredQueue.slice(-25); // Keep only last 25 events
        await AsyncStorage.setItem('analytics_event_queue', JSON.stringify(this.eventQueue));
      } else {
        await AsyncStorage.setItem('analytics_event_queue', queueData);
      }

    } catch (error) {
      console.warn('⚠️ Failed to save event queue, clearing queue:', error.message);
      // Clear the queue if we can't save it
      this.eventQueue = [];
      try {
        await AsyncStorage.removeItem('analytics_event_queue');
      } catch (clearError) {
        // Ignore clear errors
      }
    }
  }

  // Track screen views
  async trackScreenView(screenName: string, properties: Record<string, any> = {}): Promise<void> {
    await this.trackEvent('screen_view', {
      screen_name: screenName,
      ...properties,
    });
  }

  // Track user actions
  async trackUserAction(action: string, properties: Record<string, any> = {}): Promise<void> {
    await this.trackEvent('user_action', {
      action,
      ...properties,
    });
  }

  // Track business events
  async trackBusinessEvent(eventType: string, properties: Record<string, any> = {}): Promise<void> {
    await this.trackEvent('business_event', {
      event_type: eventType,
      ...properties,
    }, true); // Send immediately for business events
  }

  // Track performance metrics
  async trackPerformance(metric: string, value: number, properties: Record<string, any> = {}): Promise<void> {
    await this.trackEvent('performance_metric', {
      metric,
      value,
      ...properties,
    });
  }

  // Track errors
  async trackError(error: Error, context: string, properties: Record<string, any> = {}): Promise<void> {
    await this.trackEvent('error', {
      error_message: error.message,
      error_stack: error.stack,
      context,
      ...properties,
    }, true); // Send immediately for errors
  }

  // Track African market specific events
  async trackAfricanMarketEvent(eventType: string, properties: Record<string, any> = {}): Promise<void> {
    await this.trackEvent('african_market_event', {
      event_type: eventType,
      country: 'CI', // Côte d'Ivoire
      currency: 'XOF',
      ...properties,
    });
  }

  // Set user properties
  async setUserProperties(properties: Record<string, any>): Promise<void> {
    try {
      if (!this.userId) return;

      const { error } = await supabase
        .from('user_analytics_properties')
        .upsert({
          user_id: this.userId,
          properties,
          updated_at: new Date().toISOString(),
        });

      if (error) throw error;
    } catch (error) {
      // Silently fail for now - analytics tables may not exist yet
      if (__DEV__) {
        console.warn('⚠️ User properties update failed (table may not exist)');
      }
    }
  }

  // Update network status
  setNetworkStatus(isOnline: boolean): void {
    this.isOnline = isOnline;
    
    if (isOnline && this.eventQueue.length > 0) {
      // Process queued events when back online
      this.processEventQueue();
    }
  }

  // Get analytics summary
  async getAnalyticsSummary(timeframe: '24h' | '7d' | '30d' = '24h'): Promise<any> {
    try {
      const { data, error } = await supabase
        .rpc('get_analytics_summary', {
          user_id: this.userId,
          timeframe,
        });

      if (error) throw error;
      return data;
    } catch (error) {
      // Silently fail for now - RPC function may not exist yet
      if (__DEV__) {
        console.warn('⚠️ Analytics summary failed (RPC may not exist)');
      }
      return null;
    }
  }

  // Flush all pending events
  async flush(): Promise<void> {
    await this.processEventQueue();
  }

  // Reset analytics (for logout)
  async reset(): Promise<void> {
    this.userId = null;
    this.userRole = null;
    this.sessionId = await this.generateSessionId();
    this.eventQueue = [];
    await AsyncStorage.removeItem('analytics_event_queue');
    await AsyncStorage.removeItem('analytics_session_id');
  }
}

// Export singleton instance
export const analyticsService = new AnalyticsService();

// Convenience functions for common events
export const Analytics = {
  // Initialize
  init: (userId?: string, userRole?: string) => analyticsService.initialize(userId, userRole),
  
  // Screen tracking
  screen: (screenName: string, properties?: Record<string, any>) => 
    analyticsService.trackScreenView(screenName, properties),
  
  // User actions
  action: (action: string, properties?: Record<string, any>) => 
    analyticsService.trackUserAction(action, properties),
  
  // Business events
  business: (eventType: string, properties?: Record<string, any>) => 
    analyticsService.trackBusinessEvent(eventType, properties),
  
  // Performance
  performance: (metric: string, value: number, properties?: Record<string, any>) => 
    analyticsService.trackPerformance(metric, value, properties),
  
  // Errors
  error: (error: Error, context: string, properties?: Record<string, any>) => 
    analyticsService.trackError(error, context, properties),
  
  // African market events
  african: (eventType: string, properties?: Record<string, any>) => 
    analyticsService.trackAfricanMarketEvent(eventType, properties),
  
  // Custom events
  track: (eventName: string, properties?: Record<string, any>) => 
    analyticsService.trackEvent(eventName, properties),
  
  // User properties
  setUser: (properties: Record<string, any>) => 
    analyticsService.setUserProperties(properties),
  
  // Network status
  setOnline: (isOnline: boolean) => analyticsService.setNetworkStatus(isOnline),
  
  // Utilities
  flush: () => analyticsService.flush(),
  reset: () => analyticsService.reset(),
  getSummary: (timeframe?: '24h' | '7d' | '30d') => analyticsService.getAnalyticsSummary(timeframe),
};

export default Analytics;
